#!/usr/bin/env python3
"""
Migration script to create document_types table and populate with initial data.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from sqlalchemy import text, inspect
from datetime import datetime, timezone

def run_migration():
    """Run the migration to create document_types table."""
    app = create_app()

    with app.app_context():
        inspector = inspect(db.engine)
        
        # Check if the table already exists
        if not inspector.has_table('document_types'):
            # Create the table
            db.session.execute(text('''
                CREATE TABLE document_types (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(50) NOT NULL UNIQUE,
                    display_name VARCHAR(100) NOT NULL,
                    description TEXT,
                    has_expiry BOOLEAN NOT NULL DEFAULT TRUE,
                    has_payment_status BOOLEAN NOT NULL DEFAULT FALSE,
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            '''))
            
            # Create index for faster lookups
            db.session.execute(text('''
                CREATE INDEX idx_document_types_name ON document_types(name);
                CREATE INDEX idx_document_types_active ON document_types(is_active);
            '''))
            
            db.session.commit()
            print("Created document_types table")
            
            # Insert initial document types (excluding "klantnaam_zoeken", adding "factuur")
            initial_document_types = [
                {
                    'name': 'offerte',
                    'display_name': 'Offerte',
                    'description': 'Prijsopgave voor klant',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'werkbon',
                    'display_name': 'Werkbon',
                    'description': 'Werkorder voor uitgevoerde werkzaamheden',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'onderhoudsbon',
                    'display_name': 'Onderhoudsbon',
                    'description': 'Bon voor onderhoudswerkzaamheden',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'onderhoudscontract',
                    'display_name': 'Onderhoudscontract',
                    'description': 'Contract voor periodiek onderhoud',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'meldkamercontract',
                    'display_name': 'Meldkamercontract',
                    'description': 'Contract met meldkamer',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'beveiligingscertificaat',
                    'display_name': 'Beveiligingscertificaat',
                    'description': 'Certificaat voor beveiligingssysteem',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'intakedocument',
                    'display_name': 'Intakedocument',
                    'description': 'Document voor intake gesprek',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'projectietekening',
                    'display_name': 'Projectietekening',
                    'description': 'Technische tekening van het project',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'beveiligingsplan',
                    'display_name': 'Beveiligingsplan',
                    'description': 'Plan voor beveiligingsmaatregelen',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'kabeltekeningen',
                    'display_name': 'Kabeltekeningen',
                    'description': 'Tekeningen van kabelinstallaties',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'checklist oplevering installatie',
                    'display_name': 'Checklist Oplevering Installatie',
                    'description': 'Checklist voor oplevering van installatie',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'vrije_documenten',
                    'display_name': 'Vrije Documenten',
                    'description': 'Overige documenten',
                    'has_expiry': True,
                    'has_payment_status': False
                },
                {
                    'name': 'factuur',
                    'display_name': 'Factuur',
                    'description': 'Factuur voor geleverde diensten of producten',
                    'has_expiry': False,
                    'has_payment_status': True
                }
            ]
            
            # Insert the document types
            for doc_type in initial_document_types:
                db.session.execute(text('''
                    INSERT INTO document_types (name, display_name, description, has_expiry, has_payment_status, is_active, created_at, updated_at)
                    VALUES (:name, :display_name, :description, :has_expiry, :has_payment_status, TRUE, NOW(), NOW())
                '''), doc_type)
            
            db.session.commit()
            print(f"Inserted {len(initial_document_types)} initial document types")
            
        else:
            print("document_types table already exists")

if __name__ == "__main__":
    run_migration()
