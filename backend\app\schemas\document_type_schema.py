# app/schemas/document_type_schema.py
from marshmallow import fields, validates, ValidationError
from app.schemas import ma
from app.models.document_type import DocumentType

class DocumentTypeSchema(ma.SQLAlchemySchema):
    """Schema for DocumentType model."""
    
    class Meta:
        """Meta class for DocumentTypeSchema."""
        model = DocumentType
        load_instance = True
    
    id = ma.auto_field(dump_only=True)
    name = fields.String(required=True)
    display_name = fields.String(required=True)
    description = fields.String(allow_none=True)
    has_expiry = fields.Boolean(default=True)
    has_payment_status = fields.Boolean(default=False)
    is_active = fields.Boolean(default=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)
    
    @validates('name')
    def validate_name(self, name):
        """Validate name field."""
        if not name:
            raise ValidationError('Name is required')
        if len(name) > 50:
            raise ValidationError('Name must be less than 50 characters')
        # Check for valid characters (alphanumeric, underscore, space)
        if not all(c.isalnum() or c in ['_', ' '] for c in name):
            raise ValidationError('Name can only contain letters, numbers, underscores, and spaces')
    
    @validates('display_name')
    def validate_display_name(self, display_name):
        """Validate display_name field."""
        if not display_name:
            raise ValidationError('Display name is required')
        if len(display_name) > 100:
            raise ValidationError('Display name must be less than 100 characters')

# Schema instances
document_type_schema = DocumentTypeSchema()
document_types_schema = DocumentTypeSchema(many=True)
