# app/controllers/document_type_controller.py
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from app.services.document_type_service import DocumentTypeService
from app.schemas.document_type_schema import document_type_schema
from app.decorators.auth_decorators import role_required
from app.decorators.rate_limit_decorator import rate_limit
import logging

logger = logging.getLogger(__name__)

document_type_bp = Blueprint('document_types', __name__)
document_type_service = DocumentTypeService()

@document_type_bp.route('', methods=['GET'], strict_slashes=False)
@jwt_required()
@rate_limit("60/minute")
def get_all_document_types():
    """
    Get all document types.
    
    Query Parameters:
        active_only (bool): Whether to return only active document types (default: true)
    
    Returns:
        JSON: List of document types.
    """
    try:
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        document_types = document_type_service.get_all_document_types(active_only)
        logger.info(f"Fetched {len(document_types)} document types")
        return jsonify(document_types), 200
    except Exception as e:
        logger.error(f"Failed to fetch document types: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_type_bp.route('/<int:document_type_id>', methods=['GET'])
@jwt_required()
@rate_limit("60/minute")
def get_document_type(document_type_id):
    """
    Get a specific document type by ID.
    
    Args:
        document_type_id (int): The ID of the document type.
    
    Returns:
        JSON: Document type data.
    """
    try:
        document_type = document_type_service.get_document_type_by_id(document_type_id)
        if not document_type:
            return jsonify({"error": "Document type not found"}), 404
        
        logger.info(f"Fetched document type {document_type_id}")
        return jsonify(document_type), 200
    except Exception as e:
        logger.error(f"Failed to fetch document type {document_type_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_type_bp.route('', methods=['POST'], strict_slashes=False)
@role_required("administrator")
@rate_limit("30/minute")
def create_document_type():
    """
    Create a new document type.
    
    Returns:
        JSON: Created document type data.
    """
    try:
        # Validate request data
        data = document_type_schema.load(request.json)
        
        # Create document type
        document_type = document_type_service.create_document_type(data)
        logger.info(f"Created document type: {document_type['name']}")
        return jsonify(document_type), 201
    except ValueError as e:
        logger.warning(f"Validation error creating document type: {str(e)}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logger.error(f"Failed to create document type: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_type_bp.route('/<int:document_type_id>', methods=['PUT'])
@role_required("administrator")
@rate_limit("30/minute")
def update_document_type(document_type_id):
    """
    Update an existing document type.
    
    Args:
        document_type_id (int): The ID of the document type to update.
    
    Returns:
        JSON: Updated document type data.
    """
    try:
        # Validate request data
        data = document_type_schema.load(request.json, partial=True)
        
        # Update document type
        document_type = document_type_service.update_document_type(document_type_id, data)
        if not document_type:
            return jsonify({"error": "Document type not found"}), 404
        
        logger.info(f"Updated document type {document_type_id}")
        return jsonify(document_type), 200
    except ValueError as e:
        logger.warning(f"Validation error updating document type {document_type_id}: {str(e)}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logger.error(f"Failed to update document type {document_type_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@document_type_bp.route('/<int:document_type_id>', methods=['DELETE'])
@role_required("administrator")
@rate_limit("30/minute")
def delete_document_type(document_type_id):
    """
    Soft delete a document type.
    
    Args:
        document_type_id (int): The ID of the document type to delete.
    
    Returns:
        JSON: Success message.
    """
    try:
        success = document_type_service.delete_document_type(document_type_id)
        if not success:
            return jsonify({"error": "Document type not found"}), 404
        
        logger.info(f"Deleted document type {document_type_id}")
        return jsonify({"message": "Document type deleted successfully"}), 200
    except Exception as e:
        logger.error(f"Failed to delete document type {document_type_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500
