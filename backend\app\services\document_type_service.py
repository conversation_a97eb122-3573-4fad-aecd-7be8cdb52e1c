# app/services/document_type_service.py
from typing import List, Dict, Optional
from app.repositories.document_type_repository import DocumentTypeRepository
from app.schemas.document_type_schema import document_type_schema, document_types_schema

class DocumentTypeService:
    """Service for document type operations."""

    def __init__(self):
        self.document_type_repo = DocumentTypeRepository()

    def get_all_document_types(self, active_only: bool = True) -> List[Dict]:
        """Get all document types."""
        document_types = self.document_type_repo.get_all(active_only)
        return document_types_schema.dump(document_types)

    def get_document_type_by_id(self, document_type_id: int) -> Optional[Dict]:
        """Get document type by ID."""
        document_type = self.document_type_repo.get_by_id(document_type_id)
        if not document_type:
            return None
        return document_type_schema.dump(document_type)

    def get_document_type_by_name(self, name: str) -> Optional[Dict]:
        """Get document type by name."""
        document_type = self.document_type_repo.get_by_name(name)
        if not document_type:
            return None
        return document_type_schema.dump(document_type)

    def create_document_type(self, document_type_data: Dict) -> Dict:
        """Create a new document type."""
        # Check if name already exists
        if self.document_type_repo.exists_by_name(document_type_data['name']):
            raise ValueError(f"Document type with name '{document_type_data['name']}' already exists")
        
        document_type = self.document_type_repo.create(document_type_data)
        return document_type_schema.dump(document_type)

    def update_document_type(self, document_type_id: int, document_type_data: Dict) -> Optional[Dict]:
        """Update an existing document type."""
        # Check if name already exists (excluding current document type)
        if 'name' in document_type_data:
            if self.document_type_repo.exists_by_name(document_type_data['name'], exclude_id=document_type_id):
                raise ValueError(f"Document type with name '{document_type_data['name']}' already exists")
        
        document_type = self.document_type_repo.update(document_type_id, document_type_data)
        if not document_type:
            return None
        return document_type_schema.dump(document_type)

    def delete_document_type(self, document_type_id: int) -> bool:
        """Soft delete a document type."""
        return self.document_type_repo.delete(document_type_id)

    def get_valid_document_type_names(self, active_only: bool = True) -> List[str]:
        """Get list of valid document type names for validation."""
        document_types = self.document_type_repo.get_all(active_only)
        return [dt.name for dt in document_types]
