#!/usr/bin/env python3
"""
Migration script to add payment_status column to documents table.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from sqlalchemy import text, inspect

def run_migration():
    """Run the migration to add payment_status column to documents table."""
    app = create_app()

    with app.app_context():
        inspector = inspect(db.engine)
        
        # Check if the documents table exists
        if inspector.has_table('documents'):
            # Check if payment_status column already exists
            columns = [column['name'] for column in inspector.get_columns('documents')]
            if 'payment_status' not in columns:
                # Add the payment_status column
                db.session.execute(text("""
                    ALTER TABLE documents
                    ADD COLUMN payment_status VARCHAR(20)
                """))
                db.session.commit()
                print("Added payment_status column to documents table")
            else:
                print("payment_status column already exists in documents table")
        else:
            print("documents table does not exist")

if __name__ == "__main__":
    run_migration()
