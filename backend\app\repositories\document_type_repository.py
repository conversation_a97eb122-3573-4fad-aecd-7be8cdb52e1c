# app/repositories/document_type_repository.py
from typing import List, Optional
from app.models.document_type import DocumentType
from app import db

class DocumentTypeRepository:
    """Repository for document type operations."""

    def get_all(self, active_only: bool = True) -> List[DocumentType]:
        """Get all document types."""
        query = DocumentType.query
        if active_only:
            query = query.filter(DocumentType.is_active == True)
        return query.order_by(DocumentType.display_name).all()

    def get_by_id(self, document_type_id: int) -> Optional[DocumentType]:
        """Get document type by ID."""
        return DocumentType.query.get(document_type_id)

    def get_by_name(self, name: str) -> Optional[DocumentType]:
        """Get document type by name."""
        return DocumentType.query.filter(DocumentType.name == name).first()

    def create(self, document_type_data: dict) -> DocumentType:
        """Create a new document type."""
        document_type = DocumentType(**document_type_data)
        db.session.add(document_type)
        db.session.commit()
        return document_type

    def update(self, document_type_id: int, document_type_data: dict) -> Optional[DocumentType]:
        """Update an existing document type."""
        document_type = self.get_by_id(document_type_id)
        if not document_type:
            return None
        
        for key, value in document_type_data.items():
            if hasattr(document_type, key):
                setattr(document_type, key, value)
        
        db.session.commit()
        return document_type

    def delete(self, document_type_id: int) -> bool:
        """Soft delete a document type by setting is_active to False."""
        document_type = self.get_by_id(document_type_id)
        if not document_type:
            return False
        
        document_type.is_active = False
        db.session.commit()
        return True

    def exists_by_name(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """Check if a document type with the given name exists."""
        query = DocumentType.query.filter(DocumentType.name == name)
        if exclude_id:
            query = query.filter(DocumentType.id != exclude_id)
        return query.first() is not None
